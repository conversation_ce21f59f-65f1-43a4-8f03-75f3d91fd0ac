# BMAD Replit Helper

## Quick Start Commands

Copy and paste these into Replit chat to force BMAD compliance:

### 🚀 **Project Initialization**
```
BMAD PROJECT SETUP: Read .replit-bmad-instructions.md. Become the BMAD Analyst agent. Load bmad-core/agents/analyst.md and bmad-core/templates/project-brief-tmpl.yaml. Help me create a project brief following BMAD methodology.
```

### 📋 **Requirements Phase**
```
BMAD PM MODE: Load bmad-core/agents/pm.md and bmad-core/templates/prd-tmpl.yaml. Create a Product Requirements Document following BMAD structure. Do not use <PERSON><PERSON>'s default approach.
```

### 🏗️ **Architecture Phase**
```
BMAD ARCHITECT MODE: Load bmad-core/agents/architect.md and bmad-core/templates/architecture-tmpl.yaml. Design system architecture following BMAD methodology.
```

### 📝 **Story Creation** 
```
BMAD SM MODE: Load bmad-core/agents/sm.md and bmad-core/templates/story-tmpl.yaml. Create user stories following BMAD methodology. This is the ONLY agent for story creation.
```

### 💻 **Development Phase**
```
BMAD DEV MODE: Load bmad-core/agents/dev.md. Implement code following the stories created by SM agent. Use BMAD development practices only.
```

## 🔄 **Session Management**

### Start of Every Session:
1. Paste the appropriate BMAD activation prompt
2. Verify the agent loaded the correct bmad-core files
3. Confirm they're following BMAD methodology, not Replit defaults

### If Agent Reverts:
```
BMAD RESET: Stop using Replit logic. Read .replit-bmad-instructions.md again. Reactivate BMAD mode and ask which agent to embody.
```

### Agent Switching:
```
BMAD SWITCH: Complete current task as [CURRENT_AGENT]. Now activate [NEW_AGENT] mode by loading bmad-core/agents/[new-agent].md.
```

## 📁 **File References**

Always reference these BMAD files explicitly:
- `bmad-core/agents/` - Agent definitions
- `bmad-core/templates/` - YAML templates  
- `bmad-core/tasks/` - Task definitions
- `bmad-core/data/bmad-kb.md` - Knowledge base

## 🎯 **Success Indicators**

You know BMAD mode is working when:
- Agent asks which BMAD agent to embody
- Agent references bmad-core/ files
- Agent follows YAML templates
- Agent stays in character for one role
- Agent doesn't use Replit's default logic
