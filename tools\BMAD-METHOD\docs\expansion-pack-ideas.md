# Expansion Pack Ideas

The BMAD Method's natural language framework can be applied to any domain. Here are ideas to inspire your own expansion packs:

## Health & Wellness Pack

### Agents

- **Fitness Coach** - Creates personalized workout plans
- **Nutrition Advisor** - Designs meal plans and tracks nutrition
- **Meditation Guide** - Leads mindfulness sessions
- **Sleep Optimizer** - Improves sleep habits

### Tasks

- `create-workout-plan` - Generates weekly exercise routines
- `analyze-nutrition` - Reviews dietary habits
- `design-meditation` - Creates guided meditation scripts

### Templates

- `workout-plan-tmpl` - Structured exercise program
- `meal-plan-tmpl` - Weekly nutrition guide
- `wellness-journal-tmpl` - Progress tracking

## Creative Writing Pack

### Agents

- **Story Architect** - Plots narratives and story structures
- **Character Developer** - Creates deep, complex characters
- **World Builder** - Designs fictional universes
- **Dialog Master** - Crafts authentic conversations

### Tasks

- `develop-plot` - Creates story outlines
- `build-character` - Develops character profiles
- `create-world` - Designs settings and cultures

### Templates

- `story-outline-tmpl` - Three-act structure
- `character-sheet-tmpl` - Detailed character profile
- `world-bible-tmpl` - Universe documentation

## Business Strategy Pack

### Agents

- **Strategy Consultant** - Develops business strategies
- **Market Analyst** - Researches market opportunities
- **Financial Advisor** - Creates financial projections
- **Operations Expert** - Optimizes business processes

### Tasks

- `swot-analysis` - Conducts SWOT analysis
- `market-research` - Analyzes market conditions
- `financial-forecast` - Projects revenue/costs

### Templates

- `business-plan-tmpl` - Complete business plan
- `market-analysis-tmpl` - Market research report
- `pitch-deck-tmpl` - Investor presentation

## Education Pack

### Agents

- **Curriculum Designer** - Creates learning pathways
- **Lesson Planner** - Develops engaging lessons
- **Assessment Creator** - Designs fair evaluations
- **Learning Coach** - Provides personalized guidance

### Tasks

- `design-curriculum` - Creates course structure
- `plan-lesson` - Develops lesson content
- `create-assessment` - Builds tests/quizzes

### Templates

- `course-outline-tmpl` - Semester plan
- `lesson-plan-tmpl` - Daily lesson structure
- `rubric-tmpl` - Assessment criteria

## Personal Development Pack

### Agents

- **Life Coach** - Guides personal growth
- **Goal Strategist** - Helps achieve objectives
- **Habit Builder** - Creates lasting habits
- **Mindset Mentor** - Develops positive thinking

### Tasks

- `goal-setting` - Defines SMART goals
- `habit-tracking` - Monitors habit formation
- `reflection-exercise` - Facilitates self-reflection

### Templates

- `goal-plan-tmpl` - Goal achievement roadmap
- `habit-tracker-tmpl` - Daily habit log
- `journal-prompts-tmpl` - Reflection questions

## Creating Your Own

To create an expansion pack:

1. **Identify the domain** - What area of expertise?
2. **Define agent roles** - Who are the experts?
3. **Create tasks** - What procedures do they follow?
4. **Design templates** - What outputs do they produce?
5. **Test with users** - Does it provide value?
6. **Share with community** - Help others benefit!

Remember: The BMAD Method works anywhere natural language instructions can guide AI assistance!
