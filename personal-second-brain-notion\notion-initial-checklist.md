# Notion Setup: Phase 1 - Building the Foundation

This guide will walk you through creating the core databases for your new "Second Brain" in Notion, based on the PARA methodology.

**Goal:** Create the empty, correctly configured containers for your Projects, Areas, Resources, and Tasks.

---

### **Step 1: Create Your Workspace**

1.  In your Notion sidebar, create a new, empty **private page**.
2.  Name it `Dashboard`, `Second Brain`, or `Command Center`.
3.  Give it an icon to make it easy to spot. All of our work will live inside this page.

---

### **Step 2: Create the Four Pillar Databases**

Inside your new `Dashboard` page, you will create four full-page databases.

1.  Type `/page` and select "Page" to create a new sub-page. Name it **Projects**.
2.  On the new "Projects" page, select the **"Table"** option from the database choices.
3.  Go back to your `Dashboard` page. Repeat this process three more times for:
    *   **Areas** (select "Table" database)
    *   **Resources** (select "Table" database)
    *   **Tasks** (select "Table" database)

You should now have four sub-pages inside your `Dashboard`, each with a new, empty table database.

---

### **Step 3: Configure the "Areas" Database**

This database holds your ongoing responsibilities.

1.  Navigate to your **Areas** database.
2.  Delete the default "Tags" property by clicking its header, then "Delete property".
3.  Add a new property:
    *   **Name:** `Type`
    *   **Type:** `Select`
    *   **Options:** Add `Business`, `Personal`, and `Investment` as options.
4.  **Action Item:** Add each of your responsibilities as a new row (page) in this database.
    *   *Examples:* `Cake Websites and More, LLC` (Type: Business), `Asheville Property Group, LLC` (Type: Business), `SummaryStream` (Type: Business), `Personal Finance` (Type: Personal), `Family` (Type: Personal).

---

### **Step 4: Configure the "Projects" Database**

This database holds your active, goal-oriented projects.

1.  Navigate to your **Projects** database.
2.  Delete the default "Tags" property.
3.  Add the following new properties:
    *   **Property 1:**
        *   **Name:** `Status`
        *   **Type:** `Select`
        *   **Options:** `Not Started`, `In Progress`, `On Hold`, `Completed`.
    *   **Property 2:**
        *   **Name:** `Priority`
        *   **Type:** `Select`
        *   **Options:** `High`, `Medium`, `Low`.
    *   **Property 3:**
        *   **Name:** `Due Date`
        *   **Type:** `Date`
    *   **Property 4 (CRITICAL):**
        *   **Name:** `Area`
        *   **Type:** `Relation`
        *   **Database:** Select your **Areas** database.
        *   **Limit:** `No limit`
        *   **Toggle on "Show on Areas"**: This creates the essential two-way link. Click "Add relation".

---

### **Step 5: Configure the "Tasks" Database**

This database holds the small, actionable steps for your projects.

1.  Navigate to your **Tasks** database.
2.  Delete the default "Tags" property.
3.  Add the following new properties:
    *   **Property 1:**
        *   **Name:** `Status`
        *   **Type:** `Select`
        *   **Options:** `To Do`, `Doing`, `Done`.
    *   **Property 2:**
        *   **Name:** `Due Date`
        *   **Type:** `Date`
    *   **Property 3:**
        *   **Name:** `Assignee`
        *   **Type:** `Person` (This will allow you to assign tasks to yourself or team members you invite to specific pages).
    *   **Property 4 (CRITICAL):**
        *   **Name:** `Project`
        *   **Type:** `Relation`
        *   **Database:** Select your **Projects** database.
        *   **Limit:** `No limit`
        *   **Toggle on "Show on Projects"**: This creates the second essential two-way link. Click "Add relation".

---

Once you have completed these steps, the foundational structure of your system is in place. Take your time with the setup, ensuring the relations are configured correctly, as they are the engine of the entire system.

Let me know when you've completed this, and we'll move on to the next phase.
