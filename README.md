# Clark Development Hub

A centralized repository for managing n8n automations, software projects, and team collaboration workflows.

## 🏗️ Repository Structure

- **`docs/`** - Documentation, planning, and team guides
- **`n8n-automations/`** - n8n workflow automations
- **`software-projects/`** - Traditional software projects
- **`integrations/`** - Cross-project integrations
- **`prompts/`** - AI assistant prompt library
- **`tools/`** - Development tools and configuration

## 🚀 Quick Start for Team Members

### First Time Setup
```bash
# Clone the repository
git clone https://github.com/clark-mackey/clark-development-hub.git
cd clark-development-hub

# Open the multi-root workspace in VS Code
code .vscode/workspace.code-workspace

# Install recommended extensions (VS Code will prompt)
# Run the setup script
./tools/development/setup.sh
```

### Daily Development
```bash
# Start development environment
docker-compose -f tools/development/docker-compose.yml up

# Work on specific projects using VS Code workspace folders
# Create feature branches for changes
git checkout -b feature/your-feature-name
```

## 👥 Team Access & Responsibilities

### 🟢 Full Access (All Team Members)
- Documentation (`/docs/`)
- Shared tools (`/tools/development/`)
- Integration projects (`/integrations/`)

### 🟡 Collaborative Access (Requires PR Review)
- Awards Site (`/software-projects/awards-site/`) - @frontend-team
- Shared components (`/software-projects/shared/`) - @frontend-team
- Team prompts (`/prompts/team/`) - @clark-mackey

### 🔴 Restricted Access (Lead Approval Required)
- n8n Automations (`/n8n-automations/`) - @automation-team
- System prompts (`/prompts/system/`) - @clark-mackey
- Core configuration (`/.vscode/`, `/.github/`) - @clark-mackey

## 📖 Documentation

- [Developer Setup Guide](docs/onboarding/developer-setup.md)
- [Project Overview](docs/onboarding/project-overview.md)
- [Contribution Guidelines](docs/onboarding/contribution-guide.md)
- [Awards Site Contributing](software-projects/awards-site/CONTRIBUTING.md)

## 🔄 Development Workflow

1. **Check existing issues/PRs** before starting work
2. **Create feature branch** from `main`
3. **Develop using workspace folders** for focused work
4. **Test locally** using provided development tools
5. **Create PR** with proper template and reviewers
6. **Address feedback** and iterate
7. **Merge** after approval from required reviewers

## 🛠️ Available Tools

- **VS Code Workspace** - Multi-root setup for organized development
- **Docker Environment** - Consistent development setup
- **GitHub Actions** - Automated testing and deployment
- **AI Assistant Integration** - Configured prompts and context

## 🎯 Current Projects

### Awards Site
- **Status**: Active Development
- **Team**: @frontend-team
- **Tech Stack**: Next.js, TypeScript, Tailwind CSS
- **Location**: `software-projects/awards-site/`

### n8n Automations
- **Status**: Active Development  
- **Team**: @automation-team
- **Focus**: Business process automation
- **Location**: `n8n-automations/`

## 📞 Getting Help

- **General Questions**: Create an issue with `question` label
- **Bug Reports**: Use the bug report template
- **Feature Requests**: Use the feature request template
- **Urgent Issues**: Contact @clark-mackey directly

---

**Note**: This repository uses CODEOWNERS for access control. Check `.github/CODEOWNERS` for specific file/folder permissions.
