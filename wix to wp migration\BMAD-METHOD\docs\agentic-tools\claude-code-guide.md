# BMad Method Guide for Claude Code

For the complete workflow, see the [BMad Workflow Guide](../bmad-workflow-guide.md).

## Installation

When running `npx bmad-method install`, select **Claude Code** as your IDE. This creates:

- `.bmad-core/` folder with all agents
- `.claude/commands/` folder with agent command files (`.md`)

## Using BMad Agents in Claude Code

Type `/agent-name` in your chat to activate an agent.

## Tips for Claude Code Users

- Commands are auto-suggested as you type `/`
- More coming soon...
