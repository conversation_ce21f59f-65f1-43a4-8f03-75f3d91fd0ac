{"folders": [{"name": " Documentation & Planning", "path": "./docs"}, {"name": " n8n Automations", "path": "./n8n-automations"}, {"name": " Awards Site", "path": "./software-projects/awards-site"}, {"name": " Integrations", "path": "./integrations"}, {"name": " Prompts", "path": "./prompts"}, {"name": " Tools & Config", "path": "./tools"}, {"name": " BMAD Method", "path": "../path/to/bmad-repo"}], "settings": {"files.exclude": {"**/node_modules": true, "**/.git": true, "**/dist": true, "**/build": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true}, "typescript.preferences.includePackageJsonAutoImports": "auto", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "extensions": {"recommendations": ["ms-vscode.vscode-json", "yzhang.markdown-all-in-one", "davidanson.vscode-markdownlint", "ms-vscode.vscode-typescript-next", "esbenp.prettier-vscode", "github.vscode-pull-request-github", "ms-vscode.vscode-docker", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-eslint"]}}