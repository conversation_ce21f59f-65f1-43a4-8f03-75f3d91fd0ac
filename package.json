{"name": "clark-development-hub", "version": "1.0.0", "description": "Core infrastructure and shared components for Clark Development ecosystem", "private": true, "workspaces": ["software-projects/*", "tools/*", "integrations/*"], "scripts": {"bootstrap": "npm install && npm run build", "build": "echo 'Building all packages...'", "test": "echo 'Running all tests...'", "lint": "echo 'Linting all packages...'", "dev": "echo 'Starting development environment...'", "clean": "echo 'Cleaning all packages...'"}, "devDependencies": {"lerna": "^7.1.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/clark-mackey/clark-development-hub.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT"}