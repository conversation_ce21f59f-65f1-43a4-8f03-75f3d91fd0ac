workflow:
  id: brownfield-fullstack
  name: Brownfield Full-Stack Enhancement
  description: >-
    Agent workflow for enhancing existing full-stack applications with new features,
    modernization, or significant changes. Handles existing system analysis and safe integration.
  type: brownfield
  project_types:
    - feature-addition
    - refactoring
    - modernization
    - integration-enhancement

  sequence:
    - step: project_analysis
      agent: architect
      action: analyze existing project and use task document-project
      creates: multiple documents per the document-project template
      notes: "Review existing documentation, codebase structure, and identify integration points. Document current system understanding before proceeding."

    - agent: pm
      creates: brownfield-prd.md
      uses: brownfield-prd-tmpl
      requires: existing_project_analysis
      notes: "Creates comprehensive brownfield PRD with existing system analysis and enhancement planning. SAVE OUTPUT: Copy final brownfield-prd.md to your project's docs/ folder."

    - agent: architect
      creates: brownfield-architecture.md
      uses: brownfield-architecture-tmpl
      requires: brownfield-prd.md
      notes: "Creates brownfield architecture with integration strategy and existing system constraints. SAVE OUTPUT: Copy final brownfield-architecture.md to your project's docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all brownfield documents for integration safety and completeness. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If <PERSON><PERSON> finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - workflow_end:
      action: move_to_ide
      notes: |
        Planning phase complete! Now transition to IDE Development:
        
        1. ENSURE DOCUMENTS ARE IN PROJECT:
           - Copy final prd.md to project's docs/prd.md
           - Copy final architecture.md to project's docs/architecture.md
           - All documents must be in the project before proceeding
        
        2. SHARD DOCUMENTS (in IDE):
           - Option A: Use PO agent to shard: @po then ask to shard docs/prd.md
           - Option B: Manual: Drag shard-doc task + docs/prd.md into chat
           - This creates docs/prd/ and docs/architecture/ folders with sharded content
        
        3. START DEVELOPMENT CYCLE:
           a. SM Agent (New Chat): @sm → *create
              - Creates next story from sharded docs
              - Review and approve story (Draft → Approved)
           
           b. Dev Agent (New Chat): @dev
              - Implements approved story
              - Updates File List with all changes
              - Marks story as "Review" when complete
           
           c. QA Agent (New Chat): @qa → review-story
              - Senior dev review with refactoring ability
              - Fixes small issues directly
              - Leaves checklist for remaining items
              - Updates story status (Review → Done or stays Review)
           
           d. If QA left unchecked items:
              - Dev Agent (New Chat): Address remaining items
              - Return to QA for final approval
        
        4. REPEAT: Continue cycle for all epic stories
        
        Reference: data#bmad-kb:IDE Development Workflow

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: Brownfield Enhancement] --> B[analyst: analyze existing project]
        B --> C[pm: brownfield-prd.md]
        C --> D[architect: brownfield-architecture.md]
        D --> E[po: validate with po-master-checklist]
        E --> F{PO finds issues?}
        F -->|Yes| G[Return to relevant agent for fixes]
        F -->|No| H[Move to IDE Environment]
        G --> E

        style H fill:#90EE90
        style C fill:#FFE4B5
        style D fill:#FFE4B5
    ```

  decision_guidance:
    when_to_use:
      - Enhancement requires coordinated stories
      - Architectural changes are needed
      - Significant integration work required
      - Risk assessment and mitigation planning necessary
      - Multiple team members will work on related changes

  handoff_prompts:
    analyst_to_pm: "Existing project analysis complete. Create comprehensive brownfield PRD with integration strategy."
    pm_to_architect: "Brownfield PRD ready. Save it as docs/brownfield-prd.md, then create the integration architecture."
    architect_to_po: "Architecture complete. Save it as docs/brownfield-architecture.md. Please validate all artifacts for integration safety."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    complete: "All brownfield planning artifacts validated and saved in docs/ folder. Move to IDE environment to begin development."
