# Replit Configuration with BMAD Integration

# IMPORTANT: This project uses BMAD methodology
# Always activate BMAD mode before starting any work
# See .replit-bmad-instructions.md for full instructions

[deployment]
run = "echo 'BMAD Project - Use .replit-agent-prompts.md to activate BMAD agents'"

[env]
BMAD_MODE = "active"
BMAD_CORE_PATH = "./bmad-core"

# Remind developers to use BMAD methodology
[nix]
channel = "stable-22_11"
